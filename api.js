import request from "@/utils/request";

// 签到配置
export function getSetting() {
    return request.get('/sign_in/setting')
}

// 检查今天是否签到
export function checkSignInToday(tokenName) {
    return request.get('/sign_in/check_sign_in', {
        params: { tokenName: tokenName }
    })
}

// 签到历史
export function getSettingHistory(tokenName, pageNum, pageSize) {
    return request.get('/sign_in/history', {
        params: {
            tokenName: tokenName,
            pageNum: pageNum,
            pageSize: pageSize // 计算连续签到时获取更多数据
        }
    })
}

// 处理签到
export function doSignIn(tokenName) {
    return request({
        url: '/sign_in/do_sign_in',
        method: 'POST',
        params: { tokenName: tokenName }
    })
}

// 获取激活码信息
export function getCodeInfo(tokenName) {
    return request.get('/fq_token/get_quota', {
        params: { tokenName: tokenName }
    })
}

// 获取额外的token信息
export function getExtraTokenInfo(tokenName) {
    return request.get('/fq_token/selectByPage', {
        params: {
            tokenName: tokenName,
            pageNum: 1,
            pageSize: 1
        }
    })
}

// 刷新额度
export function refQuota(tokenName) {
    return request.get('/fq_token/get_quota', {
        params: { tokenName: tokenName }
    })
}

// 新增激活码
export function addFqToken(tokenName, time) {
    return request({
        url: '/fq_token/insert',
        method: 'PUT',
        data: {
            tokenName: tokenName,
            tokenTime: time
        }
    })
}

// 获取用户当前token状态
export function getUserFqToken() {
    return request({
        url: '/fq_token/get_user_token',
        method: 'GET'
    })
}

